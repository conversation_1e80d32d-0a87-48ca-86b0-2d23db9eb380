"use client";

import { useOrderSocket } from "@/hooks/useOrderSocket";
import { useParams } from "next/navigation";
import {
  Check,
  Clock,
  ChefHat,
  Package,
  XCircle,
  ChevronLeft,
  CreditCard,
  FileDown,
  Edit3,
} from "lucide-react"; // Import icons
import { cancelOrder } from "@/server/user";
import DishServingStatus from "@/components/orders/DishServingStatus";
import OrderUpdateDialog from "@/components/orders/OrderUpdateDialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Indian } from "@/lib/currency";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import PaymentStatusBadge from "@/components/payment/PaymentStatusBadge";
import PaymentLinkDisplay from "@/components/payment/PaymentLinkDisplay";
import { Order, Payment } from "@/types/order";
import { downloadOrderBill } from "@/utils/pdfGenerator";

const OrderSteps = ({ status }: { status: string }) => {
  const steps = [
    { id: "pending", label: "Order Placed", icon: Clock },
    { id: "preparing", label: "Preparing", icon: ChefHat },
    { id: "ready", label: "Ready for Pickup", icon: Package },
    { id: "completed", label: "Completed", icon: Check },
  ];

  const getCurrentStep = () => {
    switch (status) {
      case "pending":
        return 0;
      case "preparing":
        return 1;
      case "ready":
        return 2;
      case "completed":
        return 3;
      case "cancelled":
        return -1;
      default:
        return 0;
    }
  };

  const currentStep = getCurrentStep();

  if (status === "cancelled") {
    return (
      <div className="flex items-center justify-center p-4 bg-red-50 rounded-lg">
        <XCircle className="text-red-500 mr-2" />
        <span className="text-red-700">Order Cancelled</span>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className="flex flex-col items-center flex-1 h-24 justify-between"
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                index <= currentStep
                  ? "bg-green-500 text-white"
                  : "bg-gray-200 text-gray-500"
              } ${index == 0 ? "rounded-l-full" : ""} ${
                index == steps.length - 1 ? "rounded-r-full" : ""
              }`}
            >
              <step.icon className="w-5 h-5" />
            </div>
            <div className="text-sm mt-2 text-center">{step.label}</div>
            {index < steps.length && (
              <div
                className={`h-1 w-full mt-2 ${
                  index <= currentStep ? "bg-green-500" : "bg-gray-200"
                }`}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const OrderStatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = () => {
    switch (status) {
      case "pending":
        return "bg-yellow-500";
      case "preparing":
        return "bg-blue-500";
      case "ready":
        return "bg-green-500";
      case "completed":
        return "bg-gray-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <span
      className={`${getStatusColor()} text-white px-3 py-1 rounded-full text-sm`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

const OrderTrackingPage = () => {
  const router = useRouter();
  const params = useParams();
  const orderId = params?.orderId as string;
  const { order, loading, socket } = useOrderSocket(orderId as string);
  const [payment, setPayment] = useState<Payment | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [cancelLoading, setCancelLoading] = useState(false);
  const [foodChainId, setFoodChainId] = useState<string | null>(null);
  const [outletId, setOutletId] = useState<string | null>(null);

  useEffect(() => {
    const foodChain = localStorage.getItem("chainId");
    const outlet = localStorage.getItem("outletId");
    localStorage.removeItem(`cart_${foodChain}_${outlet}`);
    setFoodChainId(foodChain);
    setOutletId(outlet);
  }, []);

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (!cancellationReason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }

    setCancelLoading(true);
    try {
      const response = await cancelOrder(orderId, cancellationReason);
      if (response.success) {
        toast.success("Order cancelled successfully");
        setShowCancelDialog(false);
        // Refresh the order data
        if (socket) {
          socket.emit("join-order", orderId);
        }
      } else {
        toast.error(response.message || "Failed to cancel order");
      }
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast.error("An error occurred while cancelling the order");
    } finally {
      setCancelLoading(false);
    }
  };

  // Handle order update success
  const handleOrderUpdated = () => {
    // Refresh the order data
    if (socket) {
      socket.emit("join-order", orderId);
    }
    setShowUpdateDialog(false);
    toast.success("Order updated successfully!");
  };

  // Fetch payment details if order exists
  useEffect(() => {
    if (order && order.paymentStatus === "requested") {
      const fetchPayment = async () => {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders/${orderId}/payment`,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem("user-token")}`,
              },
            }
          );

          const data = await response.json();
          if (data.success && data.data.payment) {
            setPayment(data.data.payment);
          }
        } catch (error) {
          console.error("Error fetching payment details:", error);
        }
      };

      fetchPayment();
    }
  }, [order, orderId]);

  // Listen for payment updates
  useEffect(() => {
    if (socket) {
      socket.on(
        "payment-update",
        (data: { type: string; data: { order: Order; payment: Payment } }) => {
          if (data.data.payment) {
            setPayment(data.data.payment);
          }
        }
      );

      return () => {
        socket.off("payment-update");
      };
    }
  }, [socket]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        Loading order details...
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex justify-center items-center h-screen">
        Order not found
      </div>
    );
  }

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <div className="flex justify-between mb-2">
        <Button
          onClick={() => {
            router.push(
              localStorage.getItem("to-chat")
                ? `/chat?chainId=${foodChainId}&outletId=${outletId}`
                : `/orders`
            );
            localStorage.removeItem("to-chat");
          }}
        >
          <ChevronLeft />
          Back
        </Button>

        <div className="flex gap-2 flex-wrap">
          {/* Download Bill Button */}
          <Button variant="outline" onClick={() => downloadOrderBill(order)}>
            <FileDown className="mr-1 h-4 w-4" />
            <div className="hidden md:block"> Download Bill</div>
          </Button>

          {/* Show update button for unpaid orders */}
          {order && order.paymentStatus !== "paid" && (
            <Button variant="outline" onClick={() => setShowUpdateDialog(true)}>
              <Edit3 className="mr-1 h-4 w-4" />
              <div className="hidden md:block"> Update Order</div>
            </Button>
          )}

          {/* Show cancel button only for pending orders */}
          {order && order.status === "pending" && (
            <Button
              variant="destructive"
              onClick={() => setShowCancelDialog(true)}
            >
              <XCircle className="mr-1 h-4 w-4" />
              <div className="hidden md:block"> Cancel Order</div>
            </Button>
          )}
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-md md:text-2xl font-bold">
            Order #{order.orderNumber}
          </h1>
          <OrderStatusBadge status={order.status} />
        </div>
        <OrderSteps status={order.status} />
        <div className="space-y-6">
          {/* Dish Serving Status */}
          <div>
            <h2 className="font-semibold mb-4">Order Items & Status</h2>
            <DishServingStatus items={order.items} />
          </div>

          <div className="border-t pt-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{Indian(order.totalAmount)}</span>
              </div>

              {/* Display applied offers */}
              {order.appliedOffers && order.appliedOffers.length > 0 && (
                <>
                  {order.appliedOffers.map((offer, index) => (
                    <div
                      key={index}
                      className="flex justify-between text-sm text-green-600"
                    >
                      <span>Offer Discount ({offer.offerName})</span>
                      <span>-{Indian(offer.discount)}</span>
                    </div>
                  ))}
                </>
              )}

              {/* Display coupon discount */}
              {order.couponCode && (order.couponDiscount || 0) > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Coupon Discount ({order.couponCode})</span>
                  <span>-{Indian(order.couponDiscount || 0)}</span>
                </div>
              )}

              {/* Final amount - always show if there are any discounts */}
              {((order.appliedOffers && order.appliedOffers.length > 0) ||
                (order.couponCode && (order.couponDiscount || 0) > 0)) && (
                <div className="border-t pt-2 flex justify-between font-bold text-md md:text-lg">
                  <span>Final Amount</span>
                  <span>
                    {Indian(
                      order.finalAmount ||
                        order.totalAmount -
                          (order.offerDiscount || 0) -
                          (order.couponDiscount || 0)
                    )}
                  </span>
                </div>
              )}

              {/* Show total amount if no discounts */}
              {(!order.appliedOffers || order.appliedOffers.length === 0) &&
                (!order.couponCode || (order.couponDiscount || 0) === 0) && (
                  <div className="flex justify-between font-bold text-md md:text-lg">
                    <span>Total Amount</span>
                    <span>{Indian(order.totalAmount)}</span>
                  </div>
                )}
            </div>
          </div>

          {order.specialInstructions && (
            <div className="border-t pt-4">
              <h2 className="font-semibold mb-2">Special Instructions</h2>
              <p className="text-gray-600">{order.specialInstructions}</p>
            </div>
          )}

          {/* Payment Information */}
          <div className="border-t pt-4">
            <h2 className="font-semibold mb-2 flex items-center">
              <CreditCard className="mr-2" size={18} />
              Payment Information
            </h2>

            <div className="flex items-center gap-2 mb-3">
              <span className="text-xs md:text-sm text-gray-600">Status:</span>
              <PaymentStatusBadge status={order.paymentStatus || "pending"} />

              <span className="text-xs md:text-sm text-gray-600 ml-4">
                Method:
              </span>
              <span className="text-xs md:text-sm font-medium">
                {order.paymentMethod === "online" ? "Online Payment" : "Cash"}
              </span>
            </div>

            {/* Show payment link if payment has been requested */}
            {payment && order.paymentStatus === "requested" && (
              <div className="mt-4">
                <PaymentLinkDisplay payment={payment} />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Order Update Dialog */}
      {order && (
        <OrderUpdateDialog
          order={order}
          isOpen={showUpdateDialog}
          onClose={() => setShowUpdateDialog(false)}
          onSuccess={handleOrderUpdated}
        />
      )}

      {/* Cancel Order Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
            <DialogDescription>
              Please provide a reason for cancelling this order.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for cancellation</Label>
              <Textarea
                id="reason"
                placeholder="Please explain why you're cancelling this order"
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCancelDialog(false)}
              disabled={cancelLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCancelOrder}
              disabled={cancelLoading}
              variant="destructive"
            >
              {cancelLoading ? "Cancelling..." : "Confirm Cancellation"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderTrackingPage;
